# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Logs
logs/
*.log
debug.log

# Editor directories and files
.vscode/
.idea/
.DS_Store
Thumbs.db

# OS-specific files
*.swp

# React build
client/build/
client/node_modules/

# Optional: if you're using coverage or test folders
coverage/
test-output/

# Optional: ignore database dump or data files
*.sqlite
*.db
*.bak
dump.rdb
