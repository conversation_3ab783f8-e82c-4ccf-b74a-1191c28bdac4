.layout {
  margin: 0 100px;
}

.header {
  background-color: #5ab7a6;
  padding: 10px;
  border-bottom-left-radius: 10px;
  border-bottom-right-radius: 10px;
}

.logo {
  font-size: 20px;
  font-weight: 600;
  margin: 0px !important;
  cursor: pointer;
  color: white;
}
/* .headerLogo {
  width: 50px;
  background-color: white;
  border-radius: 100%;
} */
.logoContainer {
  display: none;
  justify-content: center;
  align-items: center;
}
.logoImage {
  width: 180px;
}

.username {
  font-size: 15px;
  color: white;
}

.content {
  height: 85vh;
  box-shadow: 0 0 3px grey;
  margin-top: 20px;
  border-radius: 25px 0 0 25px;
  padding: 15px;
  background-color: white;
  overflow-y: scroll;
}
.spinner {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

/* .ant-spin-dot-item {
  background-color: #5ab7a6;
} */
.primary:hover {
  color: white !important;
}
.secondary {
  background-color: white;
  color: #000;
  border: none;
}
.secondary:hover {
  color: #000 !important;
}
.userName {
  margin-left: 10px;
  font-family: "Montserrat", sans-serif;
  /* font-weight: 600; */
}

@media screen and (max-width: 700px) {
  .layout {
    margin: 0 15px;
  }
}
